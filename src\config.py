import os

# 项目根目录
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 数据目录
DATA_DIR = os.path.join(BASE_DIR, 'data')
LOG_DIR = os.path.join(DATA_DIR, 'logs')

# 同花顺数据目录
THS_SH_DIR = r"D:\同花顺软件\同花顺\history\shase\day"
THS_SZ_DIR = r"D:\同花顺软件\同花顺\history\sznse\day"

# 数据库文件
DB_FILE = os.path.join(DATA_DIR, 'stocks.db')

# 创建必要的目录
for directory in [DATA_DIR, LOG_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# Web配置
HOST = '0.0.0.0'
PORT = 5000
DEBUG = True
