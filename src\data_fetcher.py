import akshare as ak
import pandas as pd
import numpy as np
import os
import sqlite3
from datetime import datetime, timedelta
from utils import logger
from read_ths_data import read_ths_day_file

class DataFetcher:
    def __init__(self, sh_path, sz_path, db_path):
        self.sh_path = sh_path  # 上证股票数据路径
        self.sz_path = sz_path  # 深证股票数据路径
        self.db_path = db_path  # SQLite数据库路径
        self.init_db()

    def init_db(self):
        """初始化SQLite数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票基本信息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_info (
                code TEXT PRIMARY KEY,
                name TEXT,
                market TEXT,
                last_update TEXT
            )
            ''')
            
            # 创建股票数据表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_data (
                code TEXT,
                date TEXT,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                amount REAL,
                volume REAL,
                ma5 REAL,
                ma10 REAL,
                ma20 REAL,
                ma60 REAL,
                ma250 REAL,
                rsi_6 REAL,
                rsi_12 REAL,
                macd REAL,
                macd_signal REAL,
                macd_hist REAL,
                kdj_k REAL,
                kdj_d REAL,
                kdj_j REAL,
                boll_up REAL,
                boll_mid REAL,
                boll_down REAL,
                date_created TEXT,
                PRIMARY KEY (code, date)
            )
            ''')
            
            # 创建辅助索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON stock_data(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_code_date ON stock_data(code, date)')
            
            conn.commit()
            logger.info("数据库初始化完成")
        finally:
            conn.close()

    def get_stock_list(self):
        """获取股票列表"""
        try:
            # 获取A股列表
            stocks = ak.stock_info_a_code_name()
            
            # 分别筛选上证和深证股票
            sh_stocks = stocks[stocks['code'].str.startswith('60')].copy()
            sz_stocks = stocks[stocks['code'].str.startswith('00')].copy()
            
            # 更新数据库中的股票信息
            self._update_stock_info(sh_stocks, 'SH')
            self._update_stock_info(sz_stocks, 'SZ')
            
            logger.info(f"获取到上证股票{len(sh_stocks)}个，深证股票{len(sz_stocks)}个")
            return sh_stocks, sz_stocks
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            return pd.DataFrame(), pd.DataFrame()

    def _update_stock_info(self, df, market):
        """更新股票基本信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            for _, row in df.iterrows():
                conn.execute('''
                INSERT OR REPLACE INTO stock_info (code, name, market, last_update)
                VALUES (?, ?, ?, ?)
                ''', (row['code'], row['name'], market, now))
            
            conn.commit()
        except Exception as e:
            logger.error(f"更新股票信息失败: {str(e)}")
            raise
        finally:
            conn.close()

    def read_day_data(self, symbol):
        """读取日线数据"""
        try:
            # 根据股票代码判断路径
            file_path = os.path.join(
                self.sh_path if symbol.startswith('60') else self.sz_path, 
                f"{symbol}.day"
            )
            
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                return None
            
            # 使用同花顺数据读取函数
            df = read_ths_day_file(file_path)
            
            # 重命名列以匹配数据库结构
            df = df.rename(columns={
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low',
                '收盘': 'close',
                '成交金额': 'amount',
                '成交量': 'volume',
                '日期': 'date'
            })
            
            # 添加股票代码列
            df['code'] = symbol
            
            logger.info(f"成功读取{symbol}的{len(df)}条数据")
            return df
            
        except Exception as e:
            logger.error(f"读取{symbol}数据失败: {str(e)}")
            return None

    def calculate_technical_indicators(self, df):
        """计算技术指标"""
        if df is None or df.empty:
            return df
            
        try:
            # 确保按日期升序排序
            df = df.sort_values('date')
            
            # 计算移动平均线
            for period in [5, 10, 20, 60, 250]:
                df[f'ma{period}'] = df['close'].rolling(window=period).mean()
            
            # 计算RSI
            def calculate_rsi(prices, period):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                return 100 - (100 / (1 + rs))
            
            df['rsi_6'] = calculate_rsi(df['close'], 6)
            df['rsi_12'] = calculate_rsi(df['close'], 12)
            
            # 计算MACD
            exp1 = df['close'].ewm(span=12, adjust=False).mean()
            exp2 = df['close'].ewm(span=26, adjust=False).mean()
            df['macd'] = exp1 - exp2
            df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()
            df['macd_hist'] = df['macd'] - df['macd_signal']
            
            # 计算KDJ
            low_9 = df['low'].rolling(window=9).min()
            high_9 = df['high'].rolling(window=9).max()
            rsv = ((df['close'] - low_9) / (high_9 - low_9) * 100)
            df['kdj_k'] = rsv.ewm(com=2).mean()
            df['kdj_d'] = df['kdj_k'].ewm(com=2).mean()
            df['kdj_j'] = 3 * df['kdj_k'] - 2 * df['kdj_d']
            
            # 计算布林带
            df['boll_mid'] = df['close'].rolling(window=20).mean()
            std = df['close'].rolling(window=20).std()
            df['boll_up'] = df['boll_mid'] + 2 * std
            df['boll_down'] = df['boll_mid'] - 2 * std
            
            return df
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {str(e)}")
            return df

    def get_stock_data(self, symbol, start_date=None, end_date=None):
        """从数据库获取股票数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = "SELECT * FROM stock_data WHERE code = ?"
            params = [symbol]
            
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)
                
            query += " ORDER BY date ASC"
            
            df = pd.read_sql(query, conn, params=params)
            
            if df.empty:
                logger.warning(f"未找到股票{symbol}的数据")
                return None
                
            return df
            
        except Exception as e:
            logger.error(f"获取股票{symbol}数据失败: {str(e)}")
            return None
        finally:
            conn.close()

    def save_to_db(self, df, symbol):
        """保存数据到SQLite数据库"""
        if df is None or df.empty:
            return
            
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 添加创建时间
            df['date_created'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 使用INSERT OR REPLACE语法更新数据
            df.to_sql('stock_data', conn, if_exists='replace', index=False)
            
            # 执行VACUUM以优化数据库
            conn.execute('VACUUM')
            conn.commit()
            
            logger.info(f"成功保存{symbol}的{len(df)}条数据到数据库")
            
        except Exception as e:
            logger.error(f"保存{symbol}数据失败: {str(e)}")
            raise
        finally:
            conn.close()

    def process_stock(self, symbol):
        """处理单个股票数据"""
        try:
            # 读取数据
            df = self.read_day_data(symbol)
            if df is not None:
                # 计算技术指标
                df = self.calculate_technical_indicators(df)
                # 保存到数据库
                self.save_to_db(df, symbol)
                logger.info(f"成功处理股票: {symbol}")
                return True
            return False
        except Exception as e:
            logger.error(f"处理股票{symbol}失败: {str(e)}")
            return False

    def screen_stocks(self, days_ago=30):
        """筛选满足条件的股票"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 计算起始日期
            start_date = (datetime.now() - timedelta(days=days_ago)).strftime('%Y-%m-%d')
            
            query = """
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            """
            
            result = pd.read_sql(query, conn, params=[start_date])
            logger.info(f"筛选出{len(result)}只符合条件的股票")
            return result
            
        except Exception as e:
            logger.error(f"筛选股票失败: {str(e)}")
            return pd.DataFrame()
        finally:
            conn.close()

    def export_data(self, symbol, start_date=None, end_date=None, format='csv'):
        """导出股票数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = "SELECT * FROM stock_data WHERE code = ?"
            params = [symbol]
            
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)
                
            query += " ORDER BY date DESC"
            
            df = pd.read_sql(query, conn, params=params)
            
            # 生成文件名
            filename = f"stock_data_{symbol}_{datetime.now().strftime('%Y%m%d')}"
            
            if format == 'csv':
                output_file = os.path.join('data', f"{filename}.csv")
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
            else:
                output_file = os.path.join('data', f"{filename}.xlsx")
                df.to_excel(output_file, index=False)
                
            logger.info(f"数据导出成功: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"导出数据失败: {str(e)}")
            return None
        finally:
            conn.close()
