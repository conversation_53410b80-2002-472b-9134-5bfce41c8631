<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票分析系统</title>
    <!-- 引入Element Plus -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        // 检查ECharts是否加载成功
        if (typeof echarts === 'undefined') {
            console.error('ECharts未加载成功')
        } else {
            console.log('ECharts加载成功，版本:', echarts.version)
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;
            background-color: #f5f7fa;
        }
        .container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
        }
        .sidebar {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .chart-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        #stockChart {
            width: 100%;
            height: 600px;
        }
        .stock-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .data-grid {
            margin-top: 20px;
        }
        .indicators-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .chart-group {
            display: grid;
            grid-template-rows: 3fr 1fr 1fr;
            gap: 10px;
            height: 800px;
        }
        .sub-chart {
            width: 100%;
            height: 100%;
            background-color: #fff;
            border-radius: 8px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="header">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-select
                        v-model="selectedStock"
                        filterable
                        placeholder="请选择股票"
                        @change="handleStockChange"
                        style="width: 100%">
                        <el-option-group label="上证A股">
                            <el-option
                                v-for="stock in shStocks"
                                :key="stock.code"
                                :label="`${stock.code} - ${stock.name}`"
                                :value="stock.code">
                            </el-option>
                        </el-option-group>
                        <el-option-group label="深证A股">
                            <el-option
                                v-for="stock in szStocks"
                                :key="stock.code"
                                :label="`${stock.code} - ${stock.name}`"
                                :value="stock.code">
                            </el-option>
                        </el-option-group>
                    </el-select>
                </el-col>
                <el-col :span="8">
                    <el-button type="primary" @click="exportData" :disabled="!selectedStock">
                        导出数据
                    </el-button>
                    <el-button type="success" @click="refreshData" :disabled="!selectedStock">
                        刷新数据
                    </el-button>
                    <el-button type="warning" @click="screenStocks">
                        筛选股票
                    </el-button>
                </el-col>
                <el-col :span="8">
                    <el-date-picker
                        v-model="dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :shortcuts="dateShortcuts"
                        @change="handleDateChange">
                    </el-date-picker>
                </el-col>
            </el-row>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div v-if="currentStock" class="stock-info">
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="股票代码" v-text="currentStock.code"></el-descriptions-item>
                        <el-descriptions-item label="最新价" v-text="currentStock.close"></el-descriptions-item>
                        <el-descriptions-item label="涨跌幅">
                            <span :style="{ color: currentStock.changePercent >= 0 ? 'red' : 'green' }" v-text="currentStock.changePercent + '%'">
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item label="MA5" v-text="currentStock.ma5"></el-descriptions-item>
                        <el-descriptions-item label="MA10" v-text="currentStock.ma10"></el-descriptions-item>
                        <el-descriptions-item label="MA20" v-text="currentStock.ma20"></el-descriptions-item>
                        <el-descriptions-item label="MA250" v-text="currentStock.ma250"></el-descriptions-item>
                        <el-descriptions-item label="RSI(6)">
                            <span :class="{ 'text-danger': currentStock && currentStock.rsi_6 < 30, 'text-success': currentStock && currentStock.rsi_6 > 70 }">
                                <span v-if="currentStock && currentStock.rsi_6" v-text="currentStock.rsi_6.toFixed(2)"></span>
                                <span v-else>-</span>
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item label="RSI(12)">
                            <span :class="{ 'text-danger': currentStock && currentStock.rsi_12 < 30, 'text-success': currentStock && currentStock.rsi_12 > 70 }">
                                <span v-if="currentStock && currentStock.rsi_12" v-text="currentStock.rsi_12.toFixed(2)"></span>
                                <span v-else>-</span>
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item label="MACD">
                            <span :class="{ 'text-danger': currentStock && currentStock.macd > 0, 'text-success': currentStock && currentStock.macd < 0 }">
                                <span v-if="currentStock && currentStock.macd" v-text="currentStock.macd.toFixed(2)"></span>
                                <span v-else>-</span>
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item label="KDJ">
                            K: <span v-if="currentStock && currentStock.kdj_k" v-text="currentStock.kdj_k.toFixed(2)"></span><span v-else>-</span><br>
                            D: <span v-if="currentStock && currentStock.kdj_d" v-text="currentStock.kdj_d.toFixed(2)"></span><span v-else>-</span><br>
                            J: <span v-if="currentStock && currentStock.kdj_j" v-text="currentStock.kdj_j.toFixed(2)"></span><span v-else>-</span>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>

                <div class="data-grid">
                    <h3>筛选结果</h3>
                    <el-table
                        v-if="screenedStocks.length"
                        :data="screenedStocks"
                        style="width: 100%"
                        height="400"
                        @row-click="handleStockSelect">
                        <el-table-column prop="code" label="代码" width="80"></el-table-column>
                        <el-table-column prop="name" label="名称" width="100"></el-table-column>
                        <el-table-column prop="close" label="收盘价"></el-table-column>
                    </el-table>
                    <el-empty v-else description="暂无数据"></el-empty>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-group">
                    <div id="mainChart" class="sub-chart"></div>
                    <div id="volumeChart" class="sub-chart"></div>
                    <div id="indicatorChart" class="sub-chart"></div>
                </div>
            </div>
        </div>

        <!-- 导出数据对话框 -->
        <el-dialog
            v-model="exportDialogVisible"
            title="导出数据"
            width="30%">
            <el-form :model="exportForm" label-width="100px">
                <el-form-item label="日期范围">
                    <el-date-picker
                        v-model="exportForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="导出格式">
                    <el-radio-group v-model="exportForm.format">
                        <el-radio label="csv">CSV</el-radio>
                        <el-radio label="excel">Excel</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="exportDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmExport">确认导出</el-button>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref, onMounted, onUnmounted } = Vue
        const { ElMessage } = ElementPlus

        const app = createApp({
            setup() {
                // 状态定义
                const selectedStock = ref('')
                const currentStock = ref(null)
                const shStocks = ref([])
                const szStocks = ref([])
                const screenedStocks = ref([])
                const dateRange = ref(null)
                const stockChart = ref(null)
                const exportDialogVisible = ref(false)
                const exportForm = ref({
                    dateRange: null,
                    format: 'csv'
                })
                const mainChart = ref(null)
                const volumeChart = ref(null)
                const indicatorChart = ref(null)

                // 日期快捷选项
                const dateShortcuts = [
                    {
                        text: '最近一周',
                        value: () => {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            return [start, end]
                        }
                    },
                    {
                        text: '最近一月',
                        value: () => {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            return [start, end]
                        }
                    },
                    {
                        text: '最近三月',
                        value: () => {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                            return [start, end]
                        }
                    }
                ]

                // 初始化图表
                const initCharts = () => {
                    try {
                        const mainChartDom = document.getElementById('mainChart')
                        const volumeChartDom = document.getElementById('volumeChart')
                        const indicatorChartDom = document.getElementById('indicatorChart')

                        if (!mainChartDom || !volumeChartDom || !indicatorChartDom) {
                            console.error('图表DOM元素未找到')
                            return
                        }

                        mainChart.value = echarts.init(mainChartDom)
                        volumeChart.value = echarts.init(volumeChartDom)
                        indicatorChart.value = echarts.init(indicatorChartDom)

                        // 关联图表的dataZoom
                        echarts.connect([mainChart.value, volumeChart.value, indicatorChart.value])

                        console.log('图表初始化成功')
                    } catch (error) {
                        console.error('图表初始化失败:', error)
                        ElMessage.error('图表初始化失败')
                    }
                }

                // 加载股票列表
                const loadStockList = async () => {
                    try {
                        const response = await axios.get('/api/stock-list')
                        if (response.data.status === 'success') {
                            shStocks.value = response.data.data.sh_stocks
                            szStocks.value = response.data.data.sz_stocks
                        }
                    } catch (error) {
                        ElMessage.error('获取股票列表失败')
                        console.error(error)
                    }
                }

                // 加载股票数据并绘制图表
                const loadStockData = async (symbol) => {
                    try {
                        console.log('开始加载股票数据:', symbol)
                        const response = await axios.get(`/api/stock-data/${symbol}`)
                        console.log('API响应:', response.data)

                        if (response.data.status === 'success') {
                            const data = response.data.data
                            console.log('数据条数:', data.length)
                            if (data.length > 0) {
                                drawCharts(data)
                                updateStockInfo(data[0])
                                ElMessage.success(`成功加载${symbol}的数据`)
                            } else {
                                ElMessage.warning('没有找到股票数据')
                            }
                        } else {
                            ElMessage.error(response.data.message || '获取股票数据失败')
                        }
                    } catch (error) {
                        ElMessage.error('获取股票数据失败')
                        console.error('加载股票数据错误:', error)
                    }
                }

                // 绘制图表
                const drawCharts = (data) => {
                    // 检查图表实例是否存在
                    if (!mainChart.value || !volumeChart.value || !indicatorChart.value) {
                        console.error('图表实例未初始化')
                        ElMessage.error('图表初始化失败，请刷新页面重试')
                        return
                    }

                    // K线图配置
                    const mainOption = {
                        title: { text: `${selectedStock.value} K线图` },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'cross' }
                        },
                        legend: {
                            data: ['K线', 'MA5', 'MA10', 'MA20', 'MA250']
                        },
                        grid: {
                            left: '10%',
                            right: '10%',
                            bottom: '15%'
                        },
                        xAxis: {
                            type: 'category',
                            data: data.map(item => item.date),
                            scale: true,
                            boundaryGap: false,
                            axisLine: {onZero: false},
                            splitLine: {show: false},
                            min: 'dataMin',
                            max: 'dataMax'
                        },
                        yAxis: {
                            scale: true,
                            splitArea: {
                                show: true
                            }
                        },
                        dataZoom: [
                            {
                                type: 'inside',
                                start: 50,
                                end: 100
                            },
                            {
                                show: true,
                                type: 'slider',
                                top: '90%',
                                start: 50,
                                end: 100
                            }
                        ],
                        series: [
                            {
                                name: 'K线',
                                type: 'candlestick',
                                data: data.map(item => [item.open, item.close, item.low, item.high]),
                                itemStyle: {
                                    color: '#ef232a',
                                    color0: '#14b143',
                                    borderColor: '#ef232a',
                                    borderColor0: '#14b143'
                                }
                            },
                            {
                                name: 'MA5',
                                type: 'line',
                                data: data.map(item => item.ma5),
                                smooth: true,
                                lineStyle: {
                                    opacity: 0.5
                                }
                            },
                            {
                                name: 'MA10',
                                type: 'line',
                                data: data.map(item => item.ma10),
                                smooth: true,
                                lineStyle: {
                                    opacity: 0.5
                                }
                            },
                            {
                                name: 'MA20',
                                type: 'line',
                                data: data.map(item => item.ma20),
                                smooth: true,
                                lineStyle: {
                                    opacity: 0.5
                                }
                            },
                            {
                                name: 'MA250',
                                type: 'line',
                                data: data.map(item => item.ma250),
                                smooth: true,
                                lineStyle: {
                                    opacity: 0.5
                                }
                            }
                        ]
                    }

                    mainChart.value.setOption(mainOption)

                    // 成交量图配置
                    const volumeOption = {
                        title: { text: '成交量', left: 'center' },
                        tooltip: { trigger: 'axis' },
                        grid: { left: '10%', right: '10%', height: '70%' },
                        xAxis: {
                            type: 'category',
                            data: data.map(item => item.date)
                        },
                        yAxis: { type: 'value' },
                        series: [{
                            type: 'bar',
                            data: data.map(item => item.volume),
                            itemStyle: {
                                color: (params) => {
                                    const item = data[params.dataIndex]
                                    return item.close > item.open ? '#ef232a' : '#14b143'
                                }
                            }
                        }]
                    }
                    volumeChart.value.setOption(volumeOption)

                    // 技术指标图配置
                    const indicatorOption = {
                        title: { text: '技术指标', left: 'center' },
                        tooltip: { trigger: 'axis' },
                        legend: { data: ['RSI6', 'RSI12', 'MACD', 'Signal', 'Histogram'] },
                        grid: { left: '10%', right: '10%', height: '70%' },
                        xAxis: {
                            type: 'category',
                            data: data.map(item => item.date)
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: 'RSI',
                                min: 0,
                                max: 100
                            },
                            {
                                type: 'value',
                                name: 'MACD'
                            }
                        ],
                        series: [
                            {
                                name: 'RSI6',
                                type: 'line',
                                data: data.map(item => item.rsi_6)
                            },
                            {
                                name: 'RSI12',
                                type: 'line',
                                data: data.map(item => item.rsi_12)
                            },
                            {
                                name: 'MACD',
                                type: 'line',
                                yAxisIndex: 1,
                                data: data.map(item => item.macd)
                            },
                            {
                                name: 'Signal',
                                type: 'line',
                                yAxisIndex: 1,
                                data: data.map(item => item.macd_signal)
                            },
                            {
                                name: 'Histogram',
                                type: 'bar',
                                yAxisIndex: 1,
                                data: data.map(item => item.macd_hist)
                            }
                        ]
                    }
                    indicatorChart.value.setOption(indicatorOption)
                }

                // 更新股票信息
                const updateStockInfo = (data) => {
                    if (data) {
                        currentStock.value = {
                            ...data,
                            changePercent: ((data.close - data.open) / data.open * 100).toFixed(2)
                        }
                    }
                }

                // 筛选股票
                const screenStocks = async () => {
                    try {
                        const response = await axios.get('/api/screened-stocks')
                        if (response.data.status === 'success') {
                            screenedStocks.value = response.data.data
                            ElMessage.success(`找到${screenedStocks.value.length}只符合条件的股票`)
                        }
                    } catch (error) {
                        ElMessage.error('筛选股票失败')
                        console.error(error)
                    }
                }

                // 刷新数据
                const refreshData = async () => {
                    if (selectedStock.value) {
                        try {
                            const response = await axios.post(`/api/process-stock/${selectedStock.value}`)
                            if (response.data.status === 'success') {
                                ElMessage.success('数据刷新成功')
                                loadStockData(selectedStock.value)
                            }
                        } catch (error) {
                            ElMessage.error('数据刷新失败')
                            console.error(error)
                        }
                    } else {
                        ElMessage.warning('请先选择股票')
                    }
                }

                // 导出数据
                const exportData = () => {
                    exportDialogVisible.value = true
                }

                // 确认导出
                const confirmExport = async () => {
                    try {
                        let startDate = null
                        let endDate = null

                        if (exportForm.value.dateRange && Array.isArray(exportForm.value.dateRange)) {
                            if (exportForm.value.dateRange[0]) {
                                startDate = exportForm.value.dateRange[0].toISOString().split('T')[0]
                            }
                            if (exportForm.value.dateRange[1]) {
                                endDate = exportForm.value.dateRange[1].toISOString().split('T')[0]
                            }
                        }

                        const url = `/api/export/${selectedStock.value}?format=${exportForm.value.format}`
                            + (startDate ? `&start_date=${startDate}` : '')
                            + (endDate ? `&end_date=${endDate}` : '')

                        window.location.href = url
                        exportDialogVisible.value = false
                        ElMessage.success('导出任务已开始')
                    } catch (error) {
                        console.error('导出失败:', error)
                        ElMessage.error('导出失败')
                    }
                }

                // 事件处理器
                const handleStockChange = (value) => {
                    console.log('股票选择改变:', value)
                    if (value) {
                        loadStockData(value)
                    } else {
                        console.log('没有选择股票')
                    }
                }

                const handleStockSelect = (row) => {
                    selectedStock.value = row.code
                    loadStockData(row.code)
                }

                const handleDateChange = () => {
                    if (selectedStock.value) {
                        loadStockData(selectedStock.value)
                    }
                }

                // 生命周期钩子
                onMounted(() => {
                    console.log('组件已挂载，开始初始化')
                    // 延迟初始化图表，确保DOM元素已渲染
                    setTimeout(() => {
                        initCharts()
                        loadStockList()
                    }, 100)

                    // 响应窗口大小变化
                    const handleResize = () => {
                        const m = mainChart.value
                        const v = volumeChart.value
                        const i = indicatorChart.value

                        if (m) {
                            m.resize()
                        }
                        if (v) {
                            v.resize()
                        }
                        if (i) {
                            i.resize()
                        }
                    }

                    window.addEventListener('resize', handleResize)

                    onUnmounted(() => {
                        window.removeEventListener('resize', handleResize)

                        // 销毁图表实例
                        const charts = [mainChart.value, volumeChart.value, indicatorChart.value]
                        charts.forEach(chart => {
                            if (chart) {
                                chart.dispose()
                            }
                        })
                    })
                })

                return {
                    selectedStock,
                    currentStock,
                    shStocks,
                    szStocks,
                    screenedStocks,
                    dateRange,
                    dateShortcuts,
                    handleStockChange,
                    handleStockSelect,
                    handleDateChange,
                    screenStocks,
                    refreshData,
                    exportDialogVisible,
                    exportForm,
                    exportData,
                    confirmExport
                }
            }
        })

        app.use(ElementPlus)
        app.mount('#app')
    </script>
</body>
</html>
