2025-05-26 12:14:47 [INFO] 数据库初始化完成
2025-05-26 12:14:48 [INFO] 数据库初始化完成
2025-05-26 12:36:50 [INFO] 数据库初始化完成
2025-05-26 12:36:51 [INFO] 数据库初始化完成
2025-05-26 12:41:57 [INFO] 数据库初始化完成
2025-05-26 12:41:58 [INFO] 数据库初始化完成
2025-05-26 12:59:10 [INFO] 数据库初始化完成
2025-05-26 12:59:12 [INFO] 数据库初始化完成
2025-05-26 13:05:56 [INFO] 数据库初始化完成
2025-05-26 13:05:57 [INFO] 数据库初始化完成
2025-05-26 13:15:56 [INFO] 数据库初始化完成
2025-05-26 13:15:57 [INFO] 数据库初始化完成
2025-05-26 13:20:54 [INFO] 数据库初始化完成
2025-05-26 13:20:55 [INFO] 数据库初始化完成
2025-05-26 13:22:20 [INFO] 数据库初始化完成
2025-05-26 13:22:21 [INFO] 数据库初始化完成
2025-05-26 13:23:19 [INFO] 数据库初始化完成
2025-05-26 13:23:20 [INFO] 数据库初始化完成
2025-05-26 13:23:58 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:00 [INFO] 获取到上证股票1695个，深证股票1486个
2025-05-26 13:24:05 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:15 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:26 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:34 [INFO] 成功读取600000的1481条数据
2025-05-26 13:24:34 [INFO] 成功保存600000的1481条数据到数据库
2025-05-26 13:24:34 [INFO] 成功处理股票: 600000
2025-05-26 13:25:08 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:25:11 [INFO] 成功读取002980的1234条数据
2025-05-26 13:25:12 [INFO] 成功保存002980的1234条数据到数据库
2025-05-26 13:25:12 [INFO] 成功处理股票: 002980
2025-05-26 13:25:12 [INFO] 成功读取002980的1234条数据
2025-05-26 13:25:12 [INFO] 成功保存002980的1234条数据到数据库
2025-05-26 13:25:12 [INFO] 成功处理股票: 002980
2025-05-26 13:33:16 [INFO] 成功读取002980的1234条数据
2025-05-26 13:33:16 [INFO] 成功保存002980的1234条数据到数据库
2025-05-26 13:33:16 [INFO] 成功处理股票: 002980
2025-05-26 13:34:59 [INFO] 数据库初始化完成
2025-05-26 13:35:29 [INFO] 数据库初始化完成
2025-05-26 13:35:32 [INFO] 数据库初始化完成
2025-05-26 13:36:18 [INFO] 数据库初始化完成
2025-05-26 13:36:42 [INFO] 数据库初始化完成
2025-05-26 13:36:43 [INFO] 数据库初始化完成
2025-05-26 13:38:16 [INFO] 成功读取002973的1299条数据
2025-05-26 13:38:22 [INFO] 成功读取002976的1261条数据
2025-05-26 13:39:38 [INFO] 成功读取600000的1481条数据
2025-05-26 13:41:17 [INFO] 数据库初始化完成
2025-05-26 13:41:19 [INFO] 数据库初始化完成
2025-05-26 13:42:48 [INFO] 成功读取002975的1283条数据
2025-05-26 13:42:56 [INFO] 成功读取603058的1477条数据
2025-05-26 13:43:03 [INFO] 成功读取603058的1477条数据
2025-05-26 13:43:05 [INFO] 成功读取603057的638条数据
2025-05-26 13:43:38 [INFO] 成功读取603059的1477条数据
2025-05-26 13:44:36 [INFO] 数据库初始化完成
2025-05-26 13:44:37 [INFO] 数据库初始化完成
2025-05-26 13:45:14 [INFO] 成功读取603056的1477条数据
2025-05-26 13:52:24 [INFO] 获取到上证股票1695个，深证股票1486个
2025-05-26 13:52:25 [INFO] 获取到上证股票1695个，深证股票1486个
2025-05-26 13:52:25 [INFO] 开始筛选股票，总共3181只股票，限制处理50只
2025-05-26 13:52:25 [INFO] 成功读取600000的1481条数据
2025-05-26 13:52:25 [INFO] 成功读取600004的1481条数据
2025-05-26 13:52:25 [INFO] 成功读取600006的1481条数据
2025-05-26 13:52:25 [INFO] 成功读取600007的1481条数据
2025-05-26 13:52:25 [INFO] 成功读取600008的1481条数据
2025-05-26 13:52:25 [INFO] 成功读取600009的1481条数据
2025-05-26 13:52:25 [INFO] 成功读取600010的1481条数据
2025-05-26 13:52:25 [INFO] 成功读取600011的1481条数据
2025-05-26 13:52:26 [INFO] 成功读取600012的1481条数据
2025-05-26 13:52:26 [INFO] 成功读取600015的1481条数据
2025-05-26 13:52:26 [INFO] 成功读取600016的1481条数据
2025-05-26 13:52:26 [INFO] 成功读取600017的1374条数据
2025-05-26 13:52:26 [INFO] 成功读取600018的1485条数据
2025-05-26 13:52:26 [INFO] 成功读取600019的1477条数据
2025-05-26 13:52:26 [INFO] 成功读取600020的1477条数据
2025-05-26 13:52:26 [INFO] 成功读取600021的4806条数据
2025-05-26 13:52:27 [INFO] 成功读取600022的1478条数据
2025-05-26 13:52:27 [INFO] 成功读取600023的1478条数据
2025-05-26 13:52:27 [INFO] 成功读取600025的1482条数据
2025-05-26 13:52:27 [INFO] 成功读取600026的1477条数据
2025-05-26 13:52:27 [INFO] 成功读取600027的1477条数据
2025-05-26 13:52:27 [INFO] 成功读取600028的1477条数据
2025-05-26 13:52:27 [INFO] 成功读取600029的1857条数据
2025-05-26 13:52:27 [INFO] 成功读取600030的1477条数据
2025-05-26 13:52:27 [INFO] 成功读取600031的1477条数据
2025-05-26 13:52:27 [INFO] 成功读取600032的966条数据
2025-05-26 13:52:28 [INFO] 成功读取600033的1477条数据
2025-05-26 13:52:28 [INFO] 成功读取600035的1477条数据
2025-05-26 13:52:28 [INFO] 成功读取600036的1477条数据
2025-05-26 13:52:28 [INFO] 成功读取600037的1477条数据
2025-05-26 13:52:28 [INFO] 成功读取600038的1477条数据
2025-05-26 13:52:28 [INFO] 成功读取600039的1477条数据
2025-05-26 13:52:28 [INFO] 成功读取600048的2963条数据
2025-05-26 13:52:28 [INFO] 成功读取600050的1477条数据
2025-05-26 13:52:28 [INFO] 成功读取600051的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600052的1971条数据
2025-05-26 13:52:29 [INFO] 成功读取600053的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600054的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600055的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600056的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600057的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600058的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600059的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600060的1477条数据
2025-05-26 13:52:29 [INFO] 成功读取600061的1477条数据
2025-05-26 13:52:30 [INFO] 成功读取600062的1477条数据
2025-05-26 13:52:30 [INFO] 成功读取600063的1477条数据
2025-05-26 13:52:30 [INFO] 成功读取600064的1477条数据
2025-05-26 13:52:30 [INFO] 成功读取600066的1477条数据
2025-05-26 13:52:30 [INFO] 成功读取600067的1477条数据
2025-05-26 13:52:30 [INFO] 筛选完成，处理了50只股票，筛选出0只符合条件的股票
2025-05-26 13:52:34 [INFO] 成功读取600004的1481条数据
2025-05-26 13:52:37 [INFO] 成功读取600006的1481条数据
2025-05-26 13:52:54 [INFO] 成功读取600004的1481条数据
2025-05-26 13:53:35 [INFO] 成功读取600004的1481条数据
2025-05-26 13:54:46 [INFO] 数据库初始化完成
2025-05-26 13:54:47 [INFO] 数据库初始化完成
2025-05-26 13:55:17 [INFO] 数据库初始化完成
2025-05-26 13:55:19 [INFO] 数据库初始化完成
2025-05-26 13:58:48 [INFO] 成功读取600006的1481条数据
2025-05-26 14:01:31 [INFO] 数据库初始化完成
2025-05-26 14:01:32 [INFO] 数据库初始化完成
2025-05-26 14:08:04 [INFO] 数据库初始化完成
2025-05-26 14:08:05 [INFO] 数据库初始化完成
2025-05-26 14:10:09 [INFO] 获取到上证股票1695个，深证股票1486个
2025-05-26 14:10:16 [INFO] 成功读取600000的1481条数据
