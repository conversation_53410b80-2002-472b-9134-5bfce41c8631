2025-05-26 12:14:47 [INFO] 数据库初始化完成
2025-05-26 12:14:48 [INFO] 数据库初始化完成
2025-05-26 12:36:50 [INFO] 数据库初始化完成
2025-05-26 12:36:51 [INFO] 数据库初始化完成
2025-05-26 12:41:57 [INFO] 数据库初始化完成
2025-05-26 12:41:58 [INFO] 数据库初始化完成
2025-05-26 12:59:10 [INFO] 数据库初始化完成
2025-05-26 12:59:12 [INFO] 数据库初始化完成
2025-05-26 13:05:56 [INFO] 数据库初始化完成
2025-05-26 13:05:57 [INFO] 数据库初始化完成
2025-05-26 13:15:56 [INFO] 数据库初始化完成
2025-05-26 13:15:57 [INFO] 数据库初始化完成
2025-05-26 13:20:54 [INFO] 数据库初始化完成
2025-05-26 13:20:55 [INFO] 数据库初始化完成
2025-05-26 13:22:20 [INFO] 数据库初始化完成
2025-05-26 13:22:21 [INFO] 数据库初始化完成
2025-05-26 13:23:19 [INFO] 数据库初始化完成
2025-05-26 13:23:20 [INFO] 数据库初始化完成
2025-05-26 13:23:58 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:00 [INFO] 获取到上证股票1695个，深证股票1486个
2025-05-26 13:24:05 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:15 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:26 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:24:34 [INFO] 成功读取600000的1481条数据
2025-05-26 13:24:34 [INFO] 成功保存600000的1481条数据到数据库
2025-05-26 13:24:34 [INFO] 成功处理股票: 600000
2025-05-26 13:25:08 [ERROR] 筛选股票失败: Execution failed on sql '
            WITH latest_data AS (
                SELECT code, date,
                       close, volume,
                       ma5, ma250,
                       rsi_6,
                       macd_hist,
                       kdj_k, kdj_d,
                       LAG(ma250, 20) OVER (PARTITION BY code ORDER BY date) as ma250_prev,
                       LAG(macd_hist, 1) OVER (PARTITION BY code ORDER BY date) as macd_hist_prev,
                       LAG(kdj_k, 1) OVER (PARTITION BY code ORDER BY date) as kdj_k_prev,
                       LAG(kdj_d, 1) OVER (PARTITION BY code ORDER BY date) as kdj_d_prev
                FROM stock_data
                WHERE date >= ?
            )
            SELECT DISTINCT 
                   ld.*,
                   si.name,
                   si.market
            FROM latest_data ld
            JOIN stock_info si ON ld.code = si.code
            WHERE ld.ma5 < ld.ma250  -- MA5在MA250下方
            AND ld.ma250 > ld.ma250_prev  -- MA250上升趋势
            AND ld.rsi_6 < 30  -- RSI超卖
            AND ld.macd_hist > ld.macd_hist_prev  -- MACD柱状图上升
            AND ld.kdj_k_prev < ld.kdj_d_prev  -- KDJ金叉
            AND ld.kdj_k > ld.kdj_d
            ORDER BY ld.market, ld.code
            ': no such column: ld.market
2025-05-26 13:25:11 [INFO] 成功读取002980的1234条数据
2025-05-26 13:25:12 [INFO] 成功保存002980的1234条数据到数据库
2025-05-26 13:25:12 [INFO] 成功处理股票: 002980
2025-05-26 13:25:12 [INFO] 成功读取002980的1234条数据
2025-05-26 13:25:12 [INFO] 成功保存002980的1234条数据到数据库
2025-05-26 13:25:12 [INFO] 成功处理股票: 002980
2025-05-26 13:33:16 [INFO] 成功读取002980的1234条数据
2025-05-26 13:33:16 [INFO] 成功保存002980的1234条数据到数据库
2025-05-26 13:33:16 [INFO] 成功处理股票: 002980
2025-05-26 13:34:59 [INFO] 数据库初始化完成
2025-05-26 13:35:29 [INFO] 数据库初始化完成
2025-05-26 13:35:32 [INFO] 数据库初始化完成
2025-05-26 13:36:18 [INFO] 数据库初始化完成
2025-05-26 13:36:42 [INFO] 数据库初始化完成
2025-05-26 13:36:43 [INFO] 数据库初始化完成
2025-05-26 13:38:16 [INFO] 成功读取002973的1299条数据
2025-05-26 13:38:22 [INFO] 成功读取002976的1261条数据
2025-05-26 13:39:38 [INFO] 成功读取600000的1481条数据
2025-05-26 13:41:17 [INFO] 数据库初始化完成
2025-05-26 13:41:19 [INFO] 数据库初始化完成
2025-05-26 13:42:48 [INFO] 成功读取002975的1283条数据
2025-05-26 13:42:56 [INFO] 成功读取603058的1477条数据
2025-05-26 13:43:03 [INFO] 成功读取603058的1477条数据
2025-05-26 13:43:05 [INFO] 成功读取603057的638条数据
2025-05-26 13:43:38 [INFO] 成功读取603059的1477条数据
