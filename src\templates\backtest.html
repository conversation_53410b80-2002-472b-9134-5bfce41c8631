<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票回测分析</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l;
            background-color: #f5f7fa;
        }
        .container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
        }
        .sidebar {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .chart-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .chart {
            width: 100%;
            height: 400px;
        }
        .results-panel {
            margin-top: 20px;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="header">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-select
                        v-model="selectedStocks"
                        multiple
                        filterable
                        placeholder="请选择股票"
                        style="width: 100%">
                        <el-option-group label="上证A股">
                            <el-option
                                v-for="stock in shStocks"
                                :key="stock.code"
                                :label="`${stock.code} - ${stock.name}`"
                                :value="stock.code">
                            </el-option>
                        </el-option-group>
                        <el-option-group label="深证A股">
                            <el-option
                                v-for="stock in szStocks"
                                :key="stock.code"
                                :label="`${stock.code} - ${stock.name}`"
                                :value="stock.code">
                            </el-option>
                        </el-option-group>
                    </el-select>
                </el-col>
                <el-col :span="8">
                    <el-date-picker
                        v-model="dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :shortcuts="dateShortcuts">
                    </el-date-picker>
                </el-col>
                <el-col :span="8">
                    <el-input-number
                        v-model="initialCapital"
                        :min="100000"
                        :max="10000000"
                        :step="100000"
                        label="初始资金">
                    </el-input-number>
                    <el-button type="primary" @click="runBacktest" :loading="loading">
                        开始回测
                    </el-button>
                </el-col>
            </el-row>
        </div>

        <div class="main-content" v-if="backtestResults">
            <div class="sidebar">
                <h3>回测结果汇总</h3>
                <el-descriptions :column="1" border>
                    <el-descriptions-item label="测试周期">
                        {{ backtestResults.summary.start_date }} 至 
                        {{ backtestResults.summary.end_date }}
                    </el-descriptions-item>
                    <el-descriptions-item label="总收益率">
                        <span :class="{ 
                            'text-success': backtestResults.summary.total_return > 0,
                            'text-danger': backtestResults.summary.total_return < 0
                        }">
                            {{ (backtestResults.summary.total_return * 100).toFixed(2) }}%
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item label="年化收益率">
                        {{ (backtestResults.summary.annual_return * 100).toFixed(2) }}%
                    </el-descriptions-item>
                    <el-descriptions-item label="夏普比率">
                        {{ backtestResults.summary.sharpe_ratio.toFixed(2) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="最大回撤">
                        {{ (backtestResults.summary.max_drawdown * 100).toFixed(2) }}%
                    </el-descriptions-item>
                    <el-descriptions-item label="交易次数">
                        {{ backtestResults.summary.trade_count }}
                    </el-descriptions-item>
                </el-descriptions>

                <div v-if="batchResults.length" class="results-panel">
                    <h3>批量回测结果</h3>
                    <el-table
                        :data="batchResults"
                        style="width: 100%"
                        :max-height="400">
                        <el-table-column prop="symbol" label="股票代码" width="100"></el-table-column>
                        <el-table-column label="总收益率" width="100">
                            <template #default="scope">
                                <span :class="{
                                    'text-success': scope.row.total_return > 0,
                                    'text-danger': scope.row.total_return < 0
                                }">
                                    {{ (scope.row.total_return * 100).toFixed(2) }}%
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="年化收益" width="100">
                            <template #default="scope">
                                {{ (scope.row.annual_return * 100).toFixed(2) }}%
                            </template>
                        </el-table-column>
                        <el-table-column prop="sharpe_ratio" label="夏普比率" width="100">
                            <template #default="scope">
                                {{ scope.row.sharpe_ratio.toFixed(2) }}
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <div class="chart-container">
                <el-tabs v-model="activeTab">
                    <el-tab-pane label="收益曲线" name="returns">
                        <div id="returnsChart" class="chart"></div>
                    </el-tab-pane>
                    <el-tab-pane label="交易信号" name="signals">
                        <div id="signalsChart" class="chart"></div>
                    </el-tab-pane>
                    <el-tab-pane label="回撤分析" name="drawdown">
                        <div id="drawdownChart" class="chart"></div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted, watch, nextTick } = Vue
        
        const app = createApp({
            setup() {
                const selectedStocks = ref([])
                const dateRange = ref(null)
                const initialCapital = ref(1000000)
                const loading = ref(false)
                const backtestResults = ref(null)
                const batchResults = ref([])
                const activeTab = ref('returns')
                const shStocks = ref([])
                const szStocks = ref([])

                const charts = {
                    returns: null,
                    signals: null,
                    drawdown: null
                }

                const dateShortcuts = [
                    {
                        text: '最近一年',
                        value: () => {
                            const end = new Date()
                            const start = new Date()
                            start.setFullYear(start.getFullYear() - 1)
                            return [start, end]
                        }
                    },
                    {
                        text: '最近两年',
                        value: () => {
                            const end = new Date()
                            const start = new Date()
                            start.setFullYear(start.getFullYear() - 2)
                            return [start, end]
                        }
                    }
                ]

                // 初始化图表
                const initCharts = () => {
                    charts.returns = echarts.init(document.getElementById('returnsChart'))
                    charts.signals = echarts.init(document.getElementById('signalsChart'))
                    charts.drawdown = echarts.init(document.getElementById('drawdownChart'))
                }

                // 加载股票列表
                const loadStockList = async () => {
                    try {
                        const response = await axios.get('/api/stock-list')
                        if (response.data.status === 'success') {
                            shStocks.value = response.data.data.sh_stocks
                            szStocks.value = response.data.data.sz_stocks
                        }
                    } catch (error) {
                        ElMessage.error('获取股票列表失败')
                        console.error(error)
                    }
                }

                // 运行回测
                const runBacktest = async () => {
                    if (!selectedStocks.value.length || !dateRange.value) {
                        ElMessage.warning('请选择股票和日期范围')
                        return
                    }

                    loading.value = true
                    try {
                        if (selectedStocks.value.length === 1) {
                            // 单个股票回测
                            const response = await axios.post(
                                `/api/backtest/${selectedStocks.value[0]}`,
                                {
                                    start_date: dateRange.value[0].toISOString().split('T')[0],
                                    end_date: dateRange.value[1].toISOString().split('T')[0],
                                    initial_capital: initialCapital.value
                                }
                            )

                            if (response.data.status === 'success') {
                                backtestResults.value = response.data.data
                                drawCharts()
                            }
                        } else {
                            // 批量回测
                            const response = await axios.post(
                                '/api/batch-backtest',
                                {
                                    symbols: selectedStocks.value,
                                    start_date: dateRange.value[0].toISOString().split('T')[0],
                                    end_date: dateRange.value[1].toISOString().split('T')[0],
                                    initial_capital: initialCapital.value
                                }
                            )

                            if (response.data.status === 'success') {
                                batchResults.value = response.data.data
                            }
                        }
                    } catch (error) {
                        ElMessage.error('回测失败')
                        console.error(error)
                    } finally {
                        loading.value = false
                    }
                }

                // 绘制图表
                const drawCharts = () => {
                    const results = backtestResults.value
                    if (!results || !results.trades || !results.signals) return

                    const data = results.trades
                    const signals = results.signals
                    const dates = data.map(item => item.date)
                    
                    // 收益曲线
                    const returnsOption = {
                        title: { text: '收益曲线' },
                        tooltip: { trigger: 'axis' },
                        xAxis: {
                            type: 'category',
                            data: dates
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}%'
                            }
                        },
                        series: [{
                            name: '收益率',
                            type: 'line',
                            data: data.map(item => (item.returns || 0) * 100),
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(58,77,233,0.8)' },
                                    { offset: 1, color: 'rgba(58,77,233,0.1)' }
                                ])
                            }
                        }]
                    }
                    if (charts.returns) {
                        charts.returns.setOption(returnsOption)
                    }

                    // 信号图
                    const signalsOption = {
                        title: { text: '交易信号' },
                        tooltip: { trigger: 'axis' },
                        legend: {
                            data: ['价格', '买入信号', '卖出信号']
                        },
                        xAxis: {
                            type: 'category',
                            data: dates
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '价格',
                                type: 'line',
                                data: data.map(item => item.price || 0)
                            },
                            {
                                name: '买入信号',
                                type: 'scatter',
                                symbolSize: 10,
                                itemStyle: { color: '#f00' },
                                data: data.map((item, index) => {
                                    if (signals[index] && signals[index].buy_signal && item.price) {
                                        return item.price
                                    }
                                    return null
                                })
                            },
                            {
                                name: '卖出信号',
                                type: 'scatter',
                                symbolSize: 10,
                                itemStyle: { color: '#0f0' },
                                data: data.map((item, index) => {
                                    if (signals[index] && signals[index].sell_signal && item.price) {
                                        return item.price
                                    }
                                    return null
                                })
                            }
                        ]
                    }
                    if (charts.signals) {
                        charts.signals.setOption(signalsOption)
                    }

                    // 回撤分析
                    const drawdowns = data.map((item, index) => {
                        if (!item.total) return 0
                        const slicedData = data.slice(0, index + 1)
                        const maxTotal = Math.max(...slicedData.map(d => d.total || 0))
                        return ((item.total / maxTotal) - 1) * 100
                    })
                    
                    const drawdownOption = {
                        title: { text: '回撤分析' },
                        tooltip: { trigger: 'axis' },
                        xAxis: {
                            type: 'category',
                            data: dates
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}%'
                            }
                        },
                        series: [{
                            name: '回撤',
                            type: 'line',
                            data: drawdowns,
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(255,0,0,0.1)' },
                                    { offset: 1, color: 'rgba(255,0,0,0.8)' }
                                ])
                            }
                        }]
                    }
                    if (charts.drawdown) {
                        charts.drawdown.setOption(drawdownOption)
                    }
                }

                // 监听标签页切换
                watch(activeTab, () => {
                    nextTick(() => {
                        Object.values(charts).forEach(chart => {
                            if (chart) {
                                chart.resize()
                            }
                        })
                    })
                })

                onMounted(() => {
                    initCharts()
                    loadStockList()
                    
                    // 响应窗口大小变化
                    window.addEventListener('resize', () => {
                        Object.values(charts).forEach(chart => {
                            if (chart) {
                                chart.resize()
                            }
                        })
                    })
                })

                return {
                    selectedStocks,
                    dateRange,
                    initialCapital,
                    loading,
                    backtestResults,
                    batchResults,
                    activeTab,
                    dateShortcuts,
                    shStocks,
                    szStocks,
                    runBacktest
                }
            }
        })

        app.use(ElementPlus)
        app.mount('#app')
    </script>
</body>
</html>
