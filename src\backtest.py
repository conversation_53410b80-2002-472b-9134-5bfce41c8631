import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from utils import logger

class BackTester:
    def __init__(self, data_fetcher):
        self.data_fetcher = data_fetcher

    def calculate_signals(self, df):
        """计算交易信号
        
        信号规则：
        1. 金叉买入：MA5上穿MA20
        2. 死叉卖出：MA5下穿MA20
        3. RSI超买卖：RSI6 > 80卖出，RSI6 < 20买入
        4. MACD金叉买入、死叉卖出
        5. KDJ金叉买入、死叉卖出
        """
        try:
            signals = pd.DataFrame(index=df.index)
            signals['position'] = 0  # 1表示持有，-1表示做空，0表示空仓
            
            # MA5穿越MA20信号
            signals['ma_cross'] = (
                (df['ma5'] > df['ma20']) & 
                (df['ma5'].shift(1) <= df['ma20'].shift(1))
            ).astype(int)
            
            signals['ma_death'] = (
                (df['ma5'] < df['ma20']) & 
                (df['ma5'].shift(1) >= df['ma20'].shift(1))
            ).astype(int)
            
            # RSI信号
            signals['rsi_buy'] = (df['rsi_6'] < 20).astype(int)
            signals['rsi_sell'] = (df['rsi_6'] > 80).astype(int)
            
            # MACD信号
            signals['macd_cross'] = (
                (df['macd'] > df['macd_signal']) & 
                (df['macd'].shift(1) <= df['macd_signal'].shift(1))
            ).astype(int)
            
            signals['macd_death'] = (
                (df['macd'] < df['macd_signal']) & 
                (df['macd'].shift(1) >= df['macd_signal'].shift(1))
            ).astype(int)
            
            # KDJ信号
            signals['kdj_cross'] = (
                (df['kdj_k'] > df['kdj_d']) & 
                (df['kdj_k'].shift(1) <= df['kdj_d'].shift(1))
            ).astype(int)
            
            signals['kdj_death'] = (
                (df['kdj_k'] < df['kdj_d']) & 
                (df['kdj_k'].shift(1) >= df['kdj_d'].shift(1))
            ).astype(int)
            
            # 综合信号
            signals['buy_signal'] = (
                (signals['ma_cross'] | signals['rsi_buy'] | 
                 signals['macd_cross'] | signals['kdj_cross'])
            ).astype(int)
            
            signals['sell_signal'] = (
                (signals['ma_death'] | signals['rsi_sell'] | 
                 signals['macd_death'] | signals['kdj_death'])
            ).astype(int)
            
            return signals
        except Exception as e:
            logger.error(f"计算交易信号失败: {str(e)}")
            return None

    def backtest(self, symbol, start_date, end_date, initial_capital=1000000):
        """执行回测
        
        参数:
            symbol: 股票代码
            start_date: 起始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            initial_capital: 初始资金，默认100万
            
        返回:
            包含回测结果的字典
        """
        try:
            # 获取股票数据
            data = self.data_fetcher.get_stock_data(symbol, start_date, end_date)
            if data is None or data.empty:
                return None
            
            # 计算交易信号
            signals = self.calculate_signals(data)
            if signals is None:
                return None
            
            # 初始化交易结果
            results = pd.DataFrame(index=data.index)
            results['price'] = data['close']
            results['cash'] = initial_capital
            results['position'] = 0  # 持仓数量
            results['holdings'] = 0.0  # 持仓市值
            results['total'] = initial_capital  # 总资产
            results['returns'] = 0.0  # 收益率
            
            position = 0
            available_cash = initial_capital
            
            # 模拟交易
            for i in range(1, len(data)):
                price = data['close'].iloc[i]
                
                # 当前持仓市值
                holdings = position * price
                
                # 检查买入信号
                if signals['buy_signal'].iloc[i] and position == 0:
                    # 计算可买入数量（考虑手续费0.0003）
                    shares = int(available_cash * 0.9997 / price / 100) * 100
                    if shares > 0:
                        cost = shares * price * (1 + 0.0003)
                        available_cash -= cost
                        position = shares
                
                # 检查卖出信号
                elif signals['sell_signal'].iloc[i] and position > 0:
                    # 计算卖出所得（考虑手续费0.0003）
                    proceeds = position * price * (1 - 0.0003)
                    available_cash += proceeds
                    position = 0
                
                # 更新结果
                results['cash'].iloc[i] = available_cash
                results['position'].iloc[i] = position
                results['holdings'].iloc[i] = position * price
                results['total'].iloc[i] = available_cash + position * price
                results['returns'].iloc[i] = (
                    results['total'].iloc[i] / initial_capital - 1
                )
            
            # 计算回测指标
            total_return = results['returns'].iloc[-1]
            annual_return = (
                (1 + total_return) ** (252 / len(results)) - 1
                if len(results) > 0 else 0
            )
            
            daily_returns = results['total'].pct_change()
            sharpe_ratio = (
                np.sqrt(252) * daily_returns.mean() / daily_returns.std()
                if len(daily_returns) > 0 else 0
            )
            
            max_drawdown = (
                (results['total'] / results['total'].cummax() - 1).min()
            )
            
            trade_dates = signals['buy_signal'].sum()
            
            return {
                'summary': {
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'trade_count': trade_dates,
                    'start_date': start_date,
                    'end_date': end_date
                },
                'trades': results.to_dict('records'),
                'signals': signals.to_dict('records')
            }
            
        except Exception as e:
            logger.error(f"回测失败: {str(e)}")
            return None

    def batch_backtest(self, symbols, start_date, end_date, initial_capital=1000000):
        """批量回测多个股票"""
        results = []
        for symbol in symbols:
            result = self.backtest(symbol, start_date, end_date, initial_capital)
            if result:
                results.append({
                    'symbol': symbol,
                    **result['summary']
                })
        
        return pd.DataFrame(results).sort_values('total_return', ascending=False)
