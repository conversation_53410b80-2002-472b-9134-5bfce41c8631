from flask import Flask, render_template, jsonify, request, send_file
from data_fetcher import DataFetcher
from backtest import BackTester
from utils import logger
import os
import sqlite3
import traceback
import pandas as pd

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False  # 支持中文JSON

# 配置数据路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
SH_PATH = r"D:\同花顺软件\同花顺\history\shase\day"
SZ_PATH = r"D:\同花顺软件\同花顺\history\sznse\day"
DB_PATH = os.path.join(BASE_DIR, "data", "stocks.db")

# 初始化数据获取器和回测器
data_fetcher = DataFetcher(SH_PATH, SZ_PATH, DB_PATH)
back_tester = BackTester(data_fetcher)

def handle_error(f):
    """错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            error_msg = f"Error in {f.__name__}: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return jsonify({
                'error': error_msg,
                'status': 'error'
            }), 500
    wrapper.__name__ = f.__name__
    return wrapper

@app.route('/')
def index():
    """渲染主页"""
    return render_template('index.html')

@app.route('/api/stock-list')
@handle_error
def get_stock_list():
    """获取股票列表API"""
    sh_stocks, sz_stocks = data_fetcher.get_stock_list()
    return jsonify({
        'status': 'success',
        'data': {
            'sh_stocks': sh_stocks.to_dict('records') if not sh_stocks.empty else [],
            'sz_stocks': sz_stocks.to_dict('records') if not sz_stocks.empty else []
        }
    })

@app.route('/api/stock-data/<symbol>')
@handle_error
def get_stock_data(symbol):
    """获取单个股票数据API"""
    # 参数验证
    if not (symbol.startswith('60') or symbol.startswith('00')):
        return jsonify({
            'status': 'error',
            'error': '无效的股票代码'
        }), 400
        
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    conn = sqlite3.connect(DB_PATH)
    try:
        query = "SELECT * FROM stock_data WHERE code = ?"
        params = [symbol]
        
        if start_date:
            query += " AND date >= ?"
            params.append(start_date)
        if end_date:
            query += " AND date <= ?"
            params.append(end_date)
            
        query += " ORDER BY date DESC"
        
        df = pd.read_sql(query, conn, params=params)
        if not df.empty:
            return jsonify({
                'status': 'success',
                'data': df.to_dict('records')
            })
        return jsonify({
            'status': 'error',
            'message': '没有找到数据',
            'data': []
        })
    finally:
        conn.close()

@app.route('/api/screened-stocks')
@handle_error
def get_screened_stocks():
    """获取筛选后的股票列表API"""
    # 获取查询参数
    days = request.args.get('days', default=30, type=int)
    
    # 执行股票筛选
    result = data_fetcher.screen_stocks(days_ago=days)
    
    if result is not None and not result.empty:
        return jsonify({
            'status': 'success',
            'data': result.to_dict('records')
        })
    return jsonify({
        'status': 'success',
        'data': []
    })

@app.route('/api/process-stock/<symbol>', methods=['POST'])
@handle_error
def process_stock(symbol):
    """处理单个股票数据API"""
    # 验证股票代码格式
    if not (symbol.startswith('60') or symbol.startswith('00')):
        return jsonify({
            'status': 'error',
            'error': '无效的股票代码'
        }), 400

    # 处理股票数据
    success = data_fetcher.process_stock(symbol)
    
    if success:
        return jsonify({
            'status': 'success',
            'message': f'成功处理股票{symbol}的数据'
        })
    return jsonify({
        'status': 'error',
        'error': f'处理股票{symbol}数据失败'
    }), 500

@app.route('/api/export/<symbol>')
@handle_error
def export_stock_data(symbol):
    """导出股票数据API"""
    # 参数验证
    if not (symbol.startswith('60') or symbol.startswith('00')):
        return jsonify({
            'status': 'error',
            'error': '无效的股票代码'
        }), 400
        
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    format = request.args.get('format', 'csv')
    
    if format not in ['csv', 'excel']:
        return jsonify({
            'status': 'error',
            'error': '不支持的导出格式'
        }), 400
    
    # 导出数据
    output_file = data_fetcher.export_data(symbol, start_date, end_date, format)
    
    if output_file:
        return send_file(
            output_file,
            mimetype='text/csv' if format == 'csv' else 'application/vnd.ms-excel',
            as_attachment=True,
            download_name=os.path.basename(output_file)
        )
    
    return jsonify({
        'status': 'error',
        'error': '导出数据失败'
    }), 500

@app.route('/api/backtest/<symbol>', methods=['POST'])
@handle_error
def run_backtest(symbol):
    """运行回测API"""
    # 参数验证
    if not (symbol.startswith('60') or symbol.startswith('00')):
        return jsonify({
            'status': 'error',
            'error': '无效的股票代码'
        }), 400
    
    data = request.get_json()
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    initial_capital = data.get('initial_capital', 1000000)
    
    if not all([start_date, end_date]):
        return jsonify({
            'status': 'error',
            'error': '缺少必要的日期参数'
        }), 400
    
    # 执行回测
    result = back_tester.backtest(symbol, start_date, end_date, initial_capital)
    
    if result:
        return jsonify({
            'status': 'success',
            'data': result
        })
    
    return jsonify({
        'status': 'error',
        'error': '回测失败'
    }), 500

@app.route('/api/batch-backtest', methods=['POST'])
@handle_error
def run_batch_backtest():
    """批量回测API"""
    data = request.get_json()
    symbols = data.get('symbols', [])
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    initial_capital = data.get('initial_capital', 1000000)
    
    if not symbols or not all([start_date, end_date]):
        return jsonify({
            'status': 'error',
            'error': '缺少必要的参数'
        }), 400
    
    # 执行批量回测
    results = back_tester.batch_backtest(symbols, start_date, end_date, initial_capital)
    
    if not results.empty:
        return jsonify({
            'status': 'success',
            'data': results.to_dict('records')
        })
    
    return jsonify({
        'status': 'success',
        'data': []
    })

@app.route('/backtest')
def backtest_page():
    """回测页面"""
    return render_template('backtest.html')

if __name__ == '__main__':
    # 设置服务器参数
    app.run(
        host='0.0.0.0',  # 允许外部访问
        port=5000,       # 端口号
        debug=True       # 开发模式
    )
