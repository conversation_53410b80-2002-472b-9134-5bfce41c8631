import struct
import os
import pandas as pd

def read_ths_day_file(file_path):
    """
    读取同花顺日线数据文件(.day)
    
    参数:
        file_path: 文件路径，例如 'D:/同花顺软件/同花顺/history/shase/day/600216.day'
        
    返回:
        pandas.DataFrame 包含日线数据
    """
    # Read file
    # ile_path = f"D:\\同花顺软件\\同花顺\\history\\shase\\day\\600216.day"

    # 读取二进制文件
    with open(file_path, 'rb') as f:
        data = f.read()
    
    # 解析文件头
    # 文件头16字节结构:
    # 0-5: 固定标识 'hd1.0\x00' (6字节)
    # 6-9: 记录数 (4字节，小端)
    # 10-11: 记录开始位置 (2字节，小端)
    # 12-13: 每条记录长度 (2字节，小端)
    # 14-15: 每记录列数 (2字节，小端)
    
    # 检查文件头标识
    header_id = data[:6]
    if header_id != b'hd1.0\x00':
        raise ValueError("不是有效的同花顺日线数据文件")
    
    # 解析文件头信息 (小端序)
    record_count = struct.unpack('<I', data[6:10])[0]  # 记录数
    start_pos = struct.unpack('<H', data[10:12])[0]    # 记录开始位置(通常192)
    record_length = struct.unpack('<H', data[12:14])[0] # 每条记录长度(通常176)
    column_count = struct.unpack('<H', data[14:16])[0]  # 每记录列数(通常44)
    
    # 准备存储结果的列表
    results = []
    
    # 处理每条记录
    for i in range(record_count):
        # 计算当前记录的开始位置
        pos = start_pos + i * record_length
        
        # 确保不会读取超出文件范围
        if pos + record_length > len(data):
            break
        
        # 提取记录数据 (44列，每列4字节)
        record_data = data[pos:pos+record_length]
        
        # 解析各个字段 (小端序)
        # 前28字节包含主要数据 (7个4字节字段)
        fields = struct.unpack('<7I', record_data[:28])
        
        # 解析日期 (格式: YYYYMMDD)
        date_int = fields[0]
        year = date_int // 10000
        month = (date_int % 10000) // 100
        day = date_int % 100
        date_str = f"{year}-{month:02d}-{day:02d}"
        
        # 解析价格和成交量 (使用特殊编码)
        def decode_value(value, control_byte):
            """
            解码价格/成交量值
            控制字节的高4位包含指数信息:
            - bit3-0: 指数值
            - bit4: 指数符号 (0=正, 1=负)
            """
            mantissa = value & 0x0FFFFFFF  # 取低28位作为尾数
            exp = (control_byte & 0x07)   # 取低3位作为指数值
            sign = -1 if (control_byte & 0x08) else 1  # 第4位决定符号
            return mantissa * (10 ** (exp * sign))
        
        # 控制字节是每个4字节字段的最高字节右移4位
        control_bytes = [b >> 4 for b in record_data[7:28:4]]
        
        # 创建记录字典
        record = {
            '日期': date_str,
            '开盘': decode_value(fields[1], control_bytes[0]),
            '最高': decode_value(fields[2], control_bytes[1]),
            '最低': decode_value(fields[3], control_bytes[2]),
            '收盘': decode_value(fields[4], control_bytes[3]),
            '成交金额': decode_value(fields[5], control_bytes[4]),
            '成交量': decode_value(fields[6], control_bytes[5])
        }
        
        results.append(record)
    
    

    # 转换为DataFrame并按日期降序排列
    df = pd.DataFrame(results)
    df['日期'] = pd.to_datetime(df['日期'])
    df = df.sort_values('日期', ascending=False)
    df.reset_index(drop=True, inplace=True)
    

    return df

# 使用示例
if __name__ == '__main__':
    file_path = 'D:/同花顺软件/同花顺/history/shase/day/600216.day'
    try:
        df = read_ths_day_file(file_path)
        # 设置pandas显示所有行
        pd.set_option('display.max_rows', None)
        # 打印前几行，如果要打印完整数据，去掉head()
        print(df.head())
    except Exception as e:
        print(f"读取文件出错: {e}")