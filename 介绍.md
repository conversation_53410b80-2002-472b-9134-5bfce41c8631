# 目标：
开发一个股票日线分析程序，功能包括： 

# 环境：
建立当前文件夹下的虚拟python环境 

# 数据获取：
1、通过Akshare下载60/00开头的A股列表，其中60开头的代码，对应本地目录“D:\同花顺软件\同花顺\history\shase\day”，00开头的代码对应本地目录“D:\同花顺软件\同花顺\history\sznse\day”；
2、各目录下的文件存放着股票日线数据，以股票代码命名且.day为后缀，数据格式已有单独函数ReadData(string symbol)提供，返回的文件格式为['代码'、'日期': 、'开盘'、'最高'、 '最低'、'收盘'、'成交金额'、 '成交量']，直接调用就行；
3、数据按日分析，将分析结果保存起来，供日后对比
4、数据存储至本地SQLite库

# 数据管理：
支持按列表逐一对目录下的文件进行取数分析 

# 筛选分析：
计算MA5/MA250，筛选出MA250上行且MA5低于MA250的股票。 

# 可视化：
HTML页面提供操作界面，并用ECharts展示K线+均线图。 
# 技术栈：
Python (Akshare/SQLite)、HTML/JS (ECharts)、Flask/FastAPI后端。 

# 开发要求：
请按此方案实现代码，并确保： 
均线计算高效（Pandas滚动窗口）。 
前端支持交互式图表展示。