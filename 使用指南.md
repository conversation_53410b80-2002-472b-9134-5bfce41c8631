# 股票分析系统使用指南

## 📋 系统概述

这是一个基于Flask + Vue.js开发的股票技术分析和回测系统，支持从同花顺数据文件读取股票数据，进行技术指标计算、智能筛选和策略回测。

## 🚀 系统启动

### 环境要求
- Python 3.8+
- 已安装依赖包：Flask, pandas, numpy, sqlite3, akshare等
- 同花顺软件及历史数据文件

### 启动步骤
1. **进入项目目录**：
   ```bash
   cd d:\Project\PyTest\Stock\Stock3
   ```

2. **激活虚拟环境**（如果使用）：
   ```bash
   venv\Scripts\activate
   ```

3. **启动服务器**：
   ```bash
   cd src
   python app.py
   ```

4. **访问系统**：
   - 主页面：http://127.0.0.1:5000
   - 回测页面：http://127.0.0.1:5000/backtest

## 📊 主要功能模块

### 1. 股票数据分析（主页面）

#### 🔍 股票选择与查看
- **位置**：页面顶部左侧下拉框
- **功能**：
  - 支持搜索功能，可输入股票代码或名称
  - 上证A股：60开头的股票代码
  - 深证A股：00开头的股票代码
- **操作**：选择股票后**自动从同花顺数据文件读取数据并显示图表**
- **数据来源**：直接从同花顺历史数据文件读取，无需预先处理

#### 📈 图表展示区域
系统右侧显示三个联动图表：

**1. K线图（主图）**
- 显示：开盘价、收盘价、最高价、最低价
- 移动平均线：MA5（蓝）、MA10（橙）、MA20（绿）、MA250（红）
- 操作：支持鼠标滚轮缩放、拖拽查看历史数据

**2. 成交量图（中图）**
- 红色柱：上涨日成交量
- 绿色柱：下跌日成交量
- 与主图时间轴联动

**3. 技术指标图（下图）**
- RSI6、RSI12：相对强弱指标（0-100区间）
- MACD：指数平滑移动平均线
- Signal：MACD信号线
- Histogram：MACD柱状图

#### 📋 股票信息面板
**位置**：页面左侧上方
**显示内容**：
- 基本信息：股票代码、最新价、涨跌幅
- 移动平均线：MA5、MA10、MA20、MA250
- 技术指标：RSI(6)、RSI(12)、MACD、KDJ(K/D/J)

**颜色编码**：
- RSI < 30：红色文字（超卖信号）
- RSI > 70：绿色文字（超买信号）
- 涨跌幅：红色（上涨）/绿色（下跌）

### 2. 智能股票筛选

#### 🎯 筛选功能
**位置**：页面顶部"筛选股票"按钮
**筛选条件**（系统自动应用）：
1. MA5 < MA250（短期均线在长期均线下方，价格相对低位）
2. MA250 > MA250_prev（长期均线上升趋势，基本面向好）
3. RSI6 < 30（超卖状态，可能反弹）
4. MACD_hist > MACD_hist_prev（MACD柱状图上升，动能增强）
5. KDJ金叉（KDJ_K上穿KDJ_D，短期买入信号）

#### 📊 筛选结果
**位置**：页面左侧下方表格
**显示内容**：
- 股票代码、股票名称、收盘价
- 点击任意行可直接查看该股票详细分析
- 空表格显示"暂无数据"

### 3. 数据管理功能

#### 🔄 数据刷新
**位置**：页面顶部"刷新数据"按钮
**功能**：
- 重新读取同花顺.day文件
- 重新计算所有技术指标
- 将数据保存到数据库（可选，主要用于回测功能）
**注意**：
- 需要先选择股票才能刷新
- **选择股票后会自动显示图表，无需手动刷新**
- 刷新功能主要用于更新数据库中的历史数据

#### 📤 数据导出
**位置**：页面顶部"导出数据"按钮
**操作流程**：
1. 点击按钮打开导出对话框
2. 选择日期范围（可选，不选则导出全部数据）
3. 选择导出格式：CSV 或 Excel
4. 点击"确认导出"自动下载文件

**导出内容**：包含完整的OHLC数据和所有技术指标

### 4. 回测分析（独立页面）

#### 🎯 回测设置
**访问**：http://127.0.0.1:5000/backtest

**设置项**：
1. **股票选择**：支持多选，可进行单股票或批量回测
2. **时间范围**：选择回测的开始和结束日期
   - 快捷选项：最近一年、最近两年
3. **初始资金**：设置回测初始投资金额（默认100万元）

#### 📈 交易策略（系统内置）
**买入信号**（满足任一条件）：
- MA5上穿MA20（金叉）
- RSI6 < 20（深度超卖）
- MACD金叉（MACD > Signal且前一日MACD <= Signal）
- KDJ金叉（K > D且前一日K <= D）

**卖出信号**（满足任一条件）：
- MA5下穿MA20（死叉）
- RSI6 > 80（深度超买）
- MACD死叉（MACD < Signal且前一日MACD >= Signal）
- KDJ死叉（K < D且前一日K >= D）

**交易规则**：
- 手续费：0.03%（买入卖出各收取）
- 买入：使用90%可用资金，按100股整数倍买入
- 卖出：全部卖出持仓股票

#### 📊 回测结果分析
**单股票回测结果**：
- **汇总指标**：
  - 总收益率：整个回测期间的总收益
  - 年化收益率：按年计算的收益率
  - 夏普比率：风险调整后的收益指标
  - 最大回撤：投资过程中的最大亏损幅度
  - 交易次数：买入信号触发次数

**可视化图表**（三个标签页）：
1. **收益曲线**：显示投资组合价值随时间变化
2. **交易信号**：在价格图上标记买入（红点）卖出（绿点）信号
3. **回撤分析**：显示投资过程中的回撤情况

**批量回测结果**：
- 表格形式显示多只股票的回测对比
- 按总收益率降序排列
- 便于筛选表现优秀的股票

## 💾 数据来源配置

### 同花顺数据路径
系统默认读取以下路径的数据文件：
- **上证数据**：`D:\同花顺软件\同花顺\history\shase\day\`
- **深证数据**：`D:\同花顺软件\同花顺\history\sznse\day\`

### 数据文件格式
- **文件扩展名**：`.day`
- **文件命名**：`股票代码.day`（如：600000.day）
- **数据内容**：包含日期、开高低收、成交量、成交额

### 数据库存储
- **数据库类型**：SQLite
- **数据库位置**：`data/stocks.db`
- **主要表**：
  - `stock_info`：股票基本信息
  - `stock_data`：股票历史数据和技术指标

## 🔧 技术指标说明

### 移动平均线（MA）
- **MA5**：5日移动平均线，短期趋势
- **MA10**：10日移动平均线，短期趋势
- **MA20**：20日移动平均线，中期趋势
- **MA250**：250日移动平均线，长期趋势（年线）

### 相对强弱指标（RSI）
- **计算周期**：6日、12日
- **数值范围**：0-100
- **判断标准**：
  - RSI < 30：超卖，可能反弹
  - RSI > 70：超买，可能回调
  - 30-70：正常区间

### MACD指标
- **MACD线**：12日EMA - 26日EMA
- **Signal线**：MACD的9日EMA
- **Histogram**：MACD - Signal
- **金叉**：MACD上穿Signal线
- **死叉**：MACD下穿Signal线

### KDJ指标
- **K值**：快速随机值
- **D值**：K值的移动平均
- **J值**：3K - 2D
- **金叉**：K线上穿D线
- **死叉**：K线下穿D线

### 布林带（BOLL）
- **中轨**：20日移动平均线
- **上轨**：中轨 + 2倍标准差
- **下轨**：中轨 - 2倍标准差

## 📝 使用建议

### 新手入门
1. **熟悉界面**：先浏览各个功能模块，了解布局
2. **选择股票**：从熟悉的股票开始分析
3. **观察指标**：重点关注RSI和MACD的变化
4. **尝试筛选**：使用智能筛选功能发现潜在机会

### 进阶使用
1. **多指标验证**：结合多个技术指标进行综合判断
2. **回测验证**：使用历史数据验证交易策略有效性
3. **批量分析**：通过筛选功能批量发现投资机会
4. **数据导出**：导出数据进行更深入的自定义分析

### 风险提示
1. **技术指标滞后性**：技术指标基于历史数据，存在滞后性
2. **市场风险**：股市有风险，投资需谨慎
3. **策略局限性**：任何策略都有失效的可能
4. **资金管理**：注意控制仓位和风险

## 🔧 故障排除

### 常见问题
1. **页面无法访问**：检查Flask服务是否正常启动
2. **股票列表为空**：检查akshare网络连接
3. **图表不显示**：检查浏览器JavaScript是否启用
4. **数据刷新失败**：检查同花顺数据文件路径是否正确

### 日志查看
- **日志位置**：`src/data/logs/`
- **日志文件**：`stock_analysis_YYYYMMDD.log`
- **日志内容**：包含系统运行状态和错误信息

## 📞 技术支持

如遇到技术问题，可以：
1. 查看系统日志文件
2. 检查Python依赖包是否完整安装
3. 确认同花顺数据文件路径配置正确
4. 检查网络连接（获取股票列表需要网络）

---

**最后更新**：2025年5月26日
**系统版本**：v1.0
**作者**：Stock Analysis System
